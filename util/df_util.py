import numpy as np


def put_cols_ahead(df, cols):
    remaining = [col for col in df.columns if col not in cols]
    filtered_cols = [col for col in cols if col in df.columns]
    return df[filtered_cols + remaining]


def rm_cols(df, cols):
    remaining = [col for col in df.columns if col not in cols]
    return df[remaining]


def split_dataframe(df, n_splits):
    """
    Randomly splits a DataFrame into n equal parts

    Args:
        df: pandas DataFrame
        n_splits: number of splits desired

    Returns:
        list of DataFrames
    """
    if n_splits <= 1:
        print("Number of splits must be greater than 1")
        return [df]

    # Create array of indices and shuffle them
    indices = np.arange(len(df))
    np.random.shuffle(indices)

    # Calculate the size of each split
    avg = len(df) // n_splits
    remainder = len(df) % n_splits

    # Split the shuffled indices
    splits = []
    start = 0
    for i in range(n_splits):
        # Add one extra item for splits until remainder is consumed
        if i < remainder:
            end = start + avg + 1
        else:
            end = start + avg
        splits.append(df.iloc[indices[start:end]])
        start = end

    return splits
